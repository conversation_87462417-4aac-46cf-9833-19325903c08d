#coding:gbk
"""
QMT止盈止损下单模块 - 测试版本
用于验证QMT框架兼容性和基本功能

主要功能：
1. 标准QMT框架 (init + handlebar)
2. 兼容性测试
3. 基本交易逻辑
"""

import numpy as np
import datetime
import time

# ============================================================================
# 全局策略状态
# ============================================================================

g_strategy_state = {
    'initialized': False,
    'account_id': None,
    'start_time': None,
    'stock_pool': [],
    'last_prices': {},
    'test_mode': True
}

# ============================================================================
# 基础配置
# ============================================================================

STRATEGY_CONFIG = {
    'max_position_ratio': 0.7,
    'min_cash_reserve': 100,
    'buy_hang_offset_ratio': 0.002,
    'sell_hang_offset_ratio': 0.002,
    'test_mode': True
}

# ============================================================================
# QMT策略框架 - 主要入口函数
# ============================================================================

def init(ContextInfo):
    """
    QMT策略初始化函数
    在策略启动时执行一次，用于初始化全局变量和配置
    """
    try:
        print("🚀 QMT止盈止损策略测试版初始化开始...")
        
        # 设置账户信息 - 兼容不同QMT版本的属性名
        account_id = None
        try:
            # 尝试不同的账户ID属性名
            if hasattr(ContextInfo, 'accID'):
                account_id = ContextInfo.accID
                print(f"✅ 使用 ContextInfo.accID: {account_id}")
            elif hasattr(ContextInfo, 'accountid'):
                account_id = ContextInfo.accountid
                print(f"✅ 使用 ContextInfo.accountid: {account_id}")
            elif hasattr(ContextInfo, 'account_id'):
                account_id = ContextInfo.account_id
                print(f"✅ 使用 ContextInfo.account_id: {account_id}")
            elif hasattr(ContextInfo, 'account'):
                account_id = ContextInfo.account
                print(f"✅ 使用 ContextInfo.account: {account_id}")
            else:
                # 如果都没有，使用默认值
                account_id = "TEST_ACCOUNT"
                print("⚠️ 无法获取账户ID，使用测试账户")
        except Exception as e:
            account_id = "TEST_ACCOUNT"
            print(f"⚠️ 获取账户ID异常: {e}，使用测试账户")
        
        g_strategy_state['account_id'] = account_id
        g_strategy_state['start_time'] = datetime.datetime.now()
        
        # 初始化股票池
        stock_pool = []
        try:
            # 尝试获取股票池
            if hasattr(ContextInfo, 'get_stock_list_in_file'):
                stock_pool = ContextInfo.get_stock_list_in_file()
                print(f"✅ 从文件获取股票池: {len(stock_pool)}只股票")
            elif hasattr(ContextInfo, 'stock_pool'):
                stock_pool = ContextInfo.stock_pool
                print(f"✅ 从属性获取股票池: {len(stock_pool)}只股票")
            elif hasattr(ContextInfo, 'stock') and ContextInfo.stock:
                # 如果只有单个股票，创建包含该股票的列表
                stock_pool = [ContextInfo.stock]
                print(f"✅ 使用当前股票: {ContextInfo.stock}")
            else:
                print("⚠️ 无法获取股票池，请手动设置")
                stock_pool = ["000001.SZ"]  # 测试用股票
        except Exception as e:
            print(f"⚠️ 获取股票池失败: {e}")
            stock_pool = ["000001.SZ"]  # 测试用股票
        
        g_strategy_state['stock_pool'] = stock_pool
        
        # 初始化每个股票的数据结构
        for stock_code in stock_pool:
            g_strategy_state['last_prices'][stock_code] = 0
        
        # 显示初始化信息
        print(f"📊 账户ID: {g_strategy_state['account_id']}")
        print(f"📊 股票池数量: {len(stock_pool)}")
        if stock_pool:
            print(f"📊 股票列表: {', '.join(stock_pool[:3])}{'...' if len(stock_pool) > 3 else ''}")
        print(f"📊 最大仓位比例: {STRATEGY_CONFIG['max_position_ratio']:.1%}")
        print(f"📊 买入偏移比例: {STRATEGY_CONFIG['buy_hang_offset_ratio']:.4f}")
        print(f"📊 卖出偏移比例: {STRATEGY_CONFIG['sell_hang_offset_ratio']:.4f}")
        
        g_strategy_state['initialized'] = True
        print("✅ QMT止盈止损策略测试版初始化完成!")
        
    except Exception as e:
        print(f"❌ 策略初始化失败: {e}")
        import traceback
        traceback.print_exc()

def handlebar(ContextInfo):
    """
    QMT策略K线处理函数
    每根K线都会执行，是策略的核心逻辑入口
    """
    try:
        # 检查初始化状态
        if not g_strategy_state['initialized']:
            print("⚠️ 策略未初始化，跳过本次处理")
            return
        
        # 获取当前股票代码
        current_stock = getattr(ContextInfo, 'stock', None)
        if not current_stock:
            print("⚠️ 无法获取当前股票代码")
            return
        
        print(f"📊 处理股票: {current_stock}")
        
        # 更新当前股票的价格
        current_price = 0
        try:
            if hasattr(ContextInfo, 'close') and len(ContextInfo.close) > 0:
                current_price = ContextInfo.close[-1]
                g_strategy_state['last_prices'][current_stock] = current_price
                print(f"📈 当前价格: {current_price:.3f}")
            else:
                print("⚠️ 无法获取价格数据")
        except Exception as e:
            print(f"⚠️ 获取价格数据失败: {e}")
        
        # 简单的测试逻辑
        if current_price > 0:
            print(f"✅ {current_stock} 数据正常，价格: {current_price:.3f}")
            
            # 测试账户信息获取
            test_account_info(ContextInfo)
            
            # 测试持仓信息获取
            test_position_info(ContextInfo, current_stock)
        
    except Exception as e:
        print(f"❌ K线处理异常: {e}")
        import traceback
        traceback.print_exc()

def test_account_info(ContextInfo):
    """测试账户信息获取"""
    try:
        account_id = g_strategy_state.get('account_id', 'TEST_ACCOUNT')
        print(f"🧪 测试账户信息获取，账户ID: {account_id}")
        
        if hasattr(ContextInfo, 'get_trade_detail_data'):
            try:
                account_info = ContextInfo.get_trade_detail_data(account_id, 'STOCK', 'ACCOUNT')
                if account_info and len(account_info) > 0:
                    account = account_info[0]
                    available_cash = account.get('m_dAvailable', 0)
                    total_assets = account.get('m_dBalance', 0)
                    print(f"💰 可用资金: {available_cash:.2f}")
                    print(f"💎 总资产: {total_assets:.2f}")
                else:
                    print("⚠️ 账户信息为空")
            except Exception as e:
                print(f"⚠️ 获取账户信息失败: {e}")
        else:
            print("⚠️ 不支持 get_trade_detail_data 方法")
            
    except Exception as e:
        print(f"❌ 测试账户信息失败: {e}")

def test_position_info(ContextInfo, stock_code):
    """测试持仓信息获取"""
    try:
        account_id = g_strategy_state.get('account_id', 'TEST_ACCOUNT')
        print(f"🧪 测试持仓信息获取，股票: {stock_code}")
        
        if hasattr(ContextInfo, 'get_trade_detail_data'):
            try:
                positions = ContextInfo.get_trade_detail_data(account_id, 'STOCK', 'POSITION')
                if positions:
                    for position in positions:
                        if position.get('m_strInstrumentID', '') == stock_code:
                            current_shares = position.get('m_nVolume', 0)
                            available_shares = position.get('m_nCanUseVolume', 0)
                            cost_price = position.get('m_dOpenPrice', 0)
                            print(f"📊 持仓股数: {current_shares}")
                            print(f"📊 可用股数: {available_shares}")
                            print(f"📊 成本价格: {cost_price:.3f}")
                            return
                    print(f"📊 {stock_code} 无持仓")
                else:
                    print("📊 无任何持仓")
            except Exception as e:
                print(f"⚠️ 获取持仓信息失败: {e}")
        else:
            print("⚠️ 不支持 get_trade_detail_data 方法")
            
    except Exception as e:
        print(f"❌ 测试持仓信息失败: {e}")

# ============================================================================
# 手动测试函数
# ============================================================================

def 测试初始化(ContextInfo):
    """手动测试初始化"""
    print("🧪 手动测试初始化...")
    init(ContextInfo)

def 测试K线处理(ContextInfo):
    """手动测试K线处理"""
    print("🧪 手动测试K线处理...")
    handlebar(ContextInfo)

def 查看状态(ContextInfo):
    """查看策略状态"""
    print("📊 策略状态:")
    print("=" * 50)
    print(f"初始化状态: {'已初始化' if g_strategy_state['initialized'] else '未初始化'}")
    print(f"账户ID: {g_strategy_state['account_id']}")
    print(f"启动时间: {g_strategy_state['start_time']}")
    print(f"股票池: {g_strategy_state['stock_pool']}")
    print(f"最新价格: {g_strategy_state['last_prices']}")
    print("=" * 50)

# ============================================================================
# 模块信息
# ============================================================================

__version__ = "2.0.0-test"
__author__ = "QMT策略优化"
__description__ = "QMT止盈止损下单模块测试版本 - 验证QMT框架兼容性"

if __name__ == "__main__":
    print(f"QMT止盈止损下单模块测试版 v{__version__}")
    print(f"作者: {__author__}")
    print(f"描述: {__description__}")
    print("\n🧪 测试版本功能:")
    print("1. 验证QMT框架兼容性")
    print("2. 测试账户ID获取")
    print("3. 测试股票池获取")
    print("4. 测试数据获取API")
    print("5. 基础错误处理")
    print("\n📝 使用方法:")
    print("1. 在QMT中导入此测试版本")
    print("2. 运行策略查看初始化结果")
    print("3. 检查控制台输出确认兼容性")
    print("4. 确认无误后使用完整版本")
